console.log("newwways stepper.js wurde geladen!")

function toSlug(str) {
	return str
		.replace(/ä/g, "ae")
		.replace(/ö/g, "oe")
		.replace(/ü/g, "ue")
		.replace(/Ä/g, "Ae")
		.replace(/Ö/g, "Oe")
		.replace(/Ü/g, "Ue")
		.replace(/ß/g, "ss")
		.toLowerCase()
		.normalize("NFD")
		.replace(/[\u0300-\u036f]/g, "")
		.replace(/[^a-z0-9]+/g, "-")
		.replace(/^-+|-+$/g, "")
}


function removeUserDeactivatedForDependencies(slug) {
	document.querySelectorAll(".nw-rechner-row").forEach((row) => {
		const requires = row.dataset.requires
		const requiredSlug = toSlug(requires)
		if (requiredSlug === slug) {
			delete row.dataset.userDeactivated
		}
	})
}



function rekursiveDeaktivierung(slug) {
	document.querySelectorAll(".nw-rechner-row").forEach((row) => {
		const requires = row.dataset.requires
		const requiredSlug = toSlug(requires)

		if (requiredSlug === slug) {
			const input = row.querySelector(".nw-anzahl-input")
			const current = parseInt(input?.value || 0)

			if (current > 0) {
				console.log(`⛔ Setze ${slug} → ${row.dataset.slug} auf 0`)
				row.dataset.userDeactivated = "true"
				input.value = 0
				input.dispatchEvent(new Event("change"))

				// 🔁 rekursiv fortsetzen
				rekursiveDeaktivierung(row.dataset.slug)
			}
		}
	})
}


/*
function validiereAbhaengigkeiten() {
	console.log("Validiere Abhängigkeiten")

	document.querySelectorAll(".nw-rechner-container").forEach((container) => {
		const rows = container.querySelectorAll(".nw-rechner-row")
		const slugToRow = {}
		const slugToCount = {}

		// 1. Zähle aktuelle Werte und speichere Referenz zur Zeile
		rows.forEach((row) => {
			const slug = row.dataset.slug
			const input = row.querySelector(".nw-anzahl-input")
			const count = parseInt(input?.value || 0)
			if (slug) {
				slugToCount[slug] = count
				slugToRow[slug] = row
			}
		})

        console.log("SlugToCount map:", slugToCount)


		// 2. Prüfe Abhängigkeiten und setze bei Bedarf Wert automatisch
		rows.forEach((row) => {
			const requires = row.dataset.requires
			const requiresMin = parseInt(row.dataset.requiresMin || 1)
			const input = row.querySelector(".nw-anzahl-input")
			const count = parseInt(input?.value || 0)

			// Clean state
			row.classList.remove("invalid-dependency")
			row.removeAttribute("title")

			if (requires && count > 0) {
                console.log("Row requires", requires, "with min", requiresMin)  
                
				const requiredSlug = toSlug(requires)
				const actualRequired = slugToCount[requiredSlug] || 0
				console.log("Actual required", actualRequired)

				if (actualRequired < requiresMin) {
                    const requiredRow = slugToRow[requiredSlug]

                    if (requiredRow && requiredRow.dataset.userDeactivated !== "true") {
                        const requiredInput = requiredRow.querySelector(".nw-anzahl-input")
                        if (requiredInput) {
                            requiredInput.value = requiresMin
                            delete requiredRow.dataset.userDeactivated // <-- wichtig!
                            requiredInput.dispatchEvent(new Event("change"))
                        }
                    }                        
				}

			}
		})
	})
}
*/

/*
function validiereAbhaengigkeiten() {
	console.log("Validiere Abhängigkeiten")

	document.querySelectorAll(".nw-rechner-container").forEach((container) => {
		const rows = container.querySelectorAll(".nw-rechner-row")
		const slugToRow = {}
		const slugToCount = {}

		// 1. Zähle aktuelle Werte und speichere Referenz zur Zeile
		rows.forEach((row) => {
			const slug = row.dataset.slug
			const input = row.querySelector(".nw-anzahl-input")
			const count = parseInt(input?.value || 0)
			if (slug) {
				slugToCount[slug] = count
				slugToRow[slug] = row
			}
		})

		console.log("SlugToCount map:", slugToCount)

		// 2. Prüfe Abhängigkeiten und setze bei Bedarf Wert automatisch
		rows.forEach((row) => {
			const requires = row.dataset.requires
			const input = row.querySelector(".nw-anzahl-input")
			const count = parseInt(input?.value || 0)

			// Clean state
			row.classList.remove("invalid-dependency")
			row.removeAttribute("title")

			if (requires && count > 0) {
				const requires = row.dataset.requires
				const numerator = parseInt(row.dataset.requiresNumerator)
				const denominator = parseInt(row.dataset.requiresDenominator)

				if (requires && count > 0 && numerator && denominator) {
					const requiredSlug = toSlug(requires)
					const actualRequired = slugToCount[requiredSlug] || 0
					const requiredMin = Math.ceil((count * denominator) / numerator)

					console.log("Row requires", requires, "min:", requiredMin, "actual:", actualRequired)

					if (actualRequired < requiredMin) {
						const requiredRow = slugToRow[requiredSlug]
						if (requiredRow && requiredRow.dataset.userDeactivated !== "true") {
							const requiredInput = requiredRow.querySelector(".nw-anzahl-input")
							if (requiredInput) {
								requiredInput.value = requiredMin
								delete requiredRow.dataset.userDeactivated
								requiredInput.dispatchEvent(new Event("change"))
							}
						}
					}
				}
			}
		})
	})
}
*/

/*
function validiereAbhaengigkeiten() {
	console.log("Validiere Abhängigkeiten (bidirektional)")

	document.querySelectorAll(".nw-rechner-container").forEach((container) => {
		const rows = Array.from(container.querySelectorAll(".nw-rechner-row"))
		const slugToRow = {}
		const slugToCount = {}

		// 1) Map aufbauen
		rows.forEach((row) => {
			const slug = row.dataset.slug
			const count = parseInt(row.querySelector(".nw-anzahl-input")?.value || 0)
			slugToRow[slug] = row
			slugToCount[slug] = count
			// Clean state
			row.classList.remove("invalid-dependency")
			row.removeAttribute("title")
		})

		// 2) UPWARD: Anforderungen hochziehen
		rows.forEach((row) => {
			const requires = row.dataset.requires
			if (!requires) return

			const slug = row.dataset.slug
			const count = slugToCount[slug]
			const num = parseInt(row.dataset.requiresNumerator, 10)
			const den = parseInt(row.dataset.requiresDenominator, 10)
			if (count > 0 && num > 0 && den > 0) {
				const reqSlug = toSlug(requires)
				const actual = slugToCount[reqSlug] || 0
				const minReq = Math.ceil((count * den) / num)

				if (actual < minReq && row.dataset.userDeactivated !== "true") {
					const reqRow = slugToRow[reqSlug]
					const inp = reqRow.querySelector(".nw-anzahl-input")
					inp.value = minReq
					delete reqRow.dataset.userDeactivated
					inp.dispatchEvent(new Event("change"))
					// update our map sofort:
					slugToCount[reqSlug] = minReq
				}
			}
		})

		// 3) DOWNWARD: Anforderungen herunterbrechen
		rows.forEach((row) => {
			const requires = row.dataset.requires
			if (!requires) return

			const slug = row.dataset.slug
			const num = parseInt(row.dataset.requiresNumerator, 10)
			const den = parseInt(row.dataset.requiresDenominator, 10)
			if (num > 0 && den > 0) {
				// parent = das, was wir benötigen
				const parentSlug = toSlug(requires)
				const parentCount = slugToCount[parentSlug] || 0
				// Maximal erlaubte Menge für das Kind
				const maxChild = Math.floor((parentCount * num) / den)
				const actual = slugToCount[slug]
				if (actual > maxChild) {
					const rowEl = slugToRow[slug]
					const inp = rowEl.querySelector(".nw-anzahl-input")
					inp.value = maxChild
					inp.dispatchEvent(new Event("change"))
					// update Map
					slugToCount[slug] = maxChild
				}
			}
		})
	})
}
*/

function validiereAbhaengigkeiten() {
	console.log("Validiere Abhängigkeiten (Up + Down Kombination)")

	document.querySelectorAll(".nw-rechner-container").forEach((container) => {
		const rows = Array.from(container.querySelectorAll(".nw-rechner-row"))
		const slugToRow = {}
		const slugToCount = {}

		// 1) Map aufbauen
		rows.forEach((row) => {
			const slug = row.dataset.slug
			const cnt = parseInt(row.querySelector(".nw-anzahl-input")?.value || 0, 10)
			slugToRow[slug] = row
			slugToCount[slug] = cnt
			row.classList.remove("invalid-dependency")
			row.removeAttribute("title")
		})

		// 2) Pro Zeile Upward + Downward ausführen
		rows.forEach((row) => {
			const requires = row.dataset.requires
			if (!requires) return // keine Abhängigkeit

			const childSlug = row.dataset.slug
			const childCnt = slugToCount[childSlug]
			const num = parseInt(row.dataset.requiresNumerator, 10)
			const den = parseInt(row.dataset.requiresDenominator, 10)
			const parentSlug = toSlug(requires)
			const parentCnt = slugToCount[parentSlug] || 0
			const childInp = row.querySelector(".nw-anzahl-input")
			const parentInp = slugToRow[parentSlug]?.querySelector(".nw-anzahl-input")

			// --- UPWARD: wenn child > 0, stelle parent >= ceil(child * den/num)
			if (childCnt > 0 && num > 0 && den > 0) {
				const minParent = Math.ceil((childCnt * den) / num)
				if (parentCnt < minParent && row.dataset.userDeactivated !== "true") {
					parentInp.value = minParent
					delete slugToRow[parentSlug].dataset.userDeactivated
					parentInp.dispatchEvent(new Event("change"))
					slugToCount[parentSlug] = minParent
				}
			}

			// --- DOWNWARD: wenn parent > 0, stelle child in ((p−1)*num/den +1) … (p*num/den)
			if (parentCnt > 0 && num > 0 && den > 0) {
				const maxChild = Math.floor((parentCnt * num) / den)
				const minChild = parentCnt > 0 ? Math.floor(((parentCnt - 1) * num) / den) + 1 : 0
				if (childCnt < minChild) {
					childInp.value = minChild
					childInp.dispatchEvent(new Event("change"))
					slugToCount[childSlug] = minChild
				} else if (childCnt > maxChild) {
					childInp.value = maxChild
					childInp.dispatchEvent(new Event("change"))
					slugToCount[childSlug] = maxChild
				}
			}
		})
	})
}
  

function adjustDependentsDownward(startSlug) {
	// 1) Hilfs-Map für schnellen Zugriff
	const slugToRow = {}
	document.querySelectorAll(".nw-rechner-row").forEach((r) => {
		slugToRow[r.dataset.slug] = r
	})

	// 2) BFS-Queue initialisieren
	const queue = [startSlug]
	const visited = new Set()

	while (queue.length) {
		const parentSlug = queue.shift()
		visited.add(parentSlug)

		const parentRow = slugToRow[parentSlug]
		const parentCount = parseInt(parentRow.querySelector(".nw-anzahl-input")?.value) || 0

		// 3) alle direkten Kinder dieses Parents
		document.querySelectorAll(".nw-rechner-row").forEach((childRow) => {
			const reqSlug = toSlug(childRow.dataset.requires || "")
			if (reqSlug !== parentSlug) return

			const num = parseInt(childRow.dataset.requiresNumerator, 10)
			const den = parseInt(childRow.dataset.requiresDenominator, 10)
			if (!(num > 0 && den > 0)) return

			// 4) Max. erlaubte Menge für das Kind
			const maxChild = Math.floor((parentCount * num) / den)
			const inp = childRow.querySelector(".nw-anzahl-input")
			const oldValue = parseInt(inp.value, 10) || 0

			// 5) **Nur reduzieren**, wenn actual > maxChild
			if (oldValue > maxChild) {
				inp.value = maxChild
				inp.dispatchEvent(new Event("change"))
			}

			// 6) Kind in die Queue, um dessen eigene Abhängigkeiten zu prüfen
			const childSlug = childRow.dataset.slug
			if (!visited.has(childSlug)) {
				queue.push(childSlug)
			}
		})
	}
}
  


function adjustDependentsRange(startSlug) {
	// 1) Map für Schnellzugriff auf jede Zeile
	const slugToRow = {}
	document.querySelectorAll(".nw-rechner-row").forEach((r) => {
		slugToRow[r.dataset.slug] = r
	})

	// 2) BFS-Queue starten
	const queue = [startSlug]
	const visited = new Set()

	while (queue.length) {
		const parentSlug = queue.shift()
		visited.add(parentSlug)

		const parentRow = slugToRow[parentSlug]
		const parentCount = parseInt(parentRow.querySelector(".nw-anzahl-input")?.value, 10) || 0

		// 3) alle direkten Kinder dieses Parents
		document.querySelectorAll(".nw-rechner-row").forEach((childRow) => {
			const reqSlug = toSlug(childRow.dataset.requires || "")
			if (reqSlug !== parentSlug) return

			const num = parseInt(childRow.dataset.requiresNumerator, 10)
			const den = parseInt(childRow.dataset.requiresDenominator, 10)
			if (!(num > 0 && den > 0)) return

			const inp = childRow.querySelector(".nw-anzahl-input")
			const oldValue = parseInt(inp.value, 10) || 0

			// 4) Range berechnen
			const maxChild = Math.floor((parentCount * num) / den)
			const minChild = parentCount > 0 ? Math.floor(((parentCount - 1) * num) / den) + 1 : 0

			// 5) Nur **anpassen**, wenn Kind außerhalb dieser Range ist
			if (oldValue < minChild) {
				inp.value = minChild
				inp.dispatchEvent(new Event("change"))
			} else if (oldValue > maxChild) {
				inp.value = maxChild
				inp.dispatchEvent(new Event("change"))
			}

			// 6) Kind weiter in die Queue, um dessen Kinder auch zu checken
			const childSlug = childRow.dataset.slug
			if (!visited.has(childSlug)) {
				queue.push(childSlug)
			}
		})
	}
}
  


function handleManualDeactivation() {
	console.log("Handle manual deactivation")

	document.querySelectorAll(".nw-rechner-row").forEach((row) => {
		const input = row.querySelector(".nw-anzahl-input")
		const count = parseInt(input?.value || 0)

		if (count === 0) {
			// Merke: vom User deaktiviert
			row.dataset.userDeactivated = "true"

			const thisSlug = row.dataset.slug

			document.querySelectorAll(".nw-rechner-row").forEach((otherRow) => {
				const requires = otherRow.dataset.requires
				const requiredSlug = toSlug(requires)

				if (requiredSlug === thisSlug) {
					const otherInput = otherRow.querySelector(".nw-anzahl-input")
					if (parseInt(otherInput?.value || 0) > 0) {
						otherInput.value = 0
						otherInput.dispatchEvent(new Event("change"))
					}
				}
			})
		} else {
			// Sobald manuell wieder aktiv → Attribut entfernen
			delete row.dataset.userDeactivated
		}
	})
}


function berechneSummenProContainer() {
	console.log("Berechne Summen pro Container") // Debugging

	document.querySelectorAll(".nw-rechner-container").forEach(function (container) {
		console.log("Container:", container)

		let summe = 0

		// Alle Elemente im Container durchgehen
		const rows = container.querySelectorAll(".nw-rechner-row")
		console.log("Gefundene Zeilen:", rows) // Debugging
		console.log("Zeilen im Container:", rows.length)

		rows.forEach(function (row) {
			const input = row.querySelector(".nw-anzahl-input")
			const preisFeld = row.querySelector(".nw-preis-wert")

			if (!input || !preisFeld) {
				console.warn("Fehlende Felder in Zeile:", row) // Debugging
				return
			}
			console.log("Datensatz:", preisFeld.dataset.preis)
			const preis = parseFloat(preisFeld.dataset.preis)
			const anzahl = parseInt(input.value) || 0

			if (!isNaN(preis) && !isNaN(anzahl)) {
				summe += anzahl * preis
			}
		})

		// Gesamtsumme anzeigen
		const mwst = summe * 0.19
		const brutto = summe + mwst

		const gesamtFeld = container.querySelector(".netto-summe-wert")
		const mwstFeld = container.querySelector(".mwst-summe-wert")
		const bruttoFeld = container.querySelector(".brutto-summe-wert")

		if (gesamtFeld) {
			gesamtFeld.textContent = summe.toFixed(2).replace(".", ",")
		}
		if (mwstFeld) {
			mwstFeld.textContent = mwst.toFixed(2).replace(".", ",")
		}
		if (bruttoFeld) {
			bruttoFeld.textContent = brutto.toFixed(2).replace(".", ",")
		}
	})
}

document.addEventListener("DOMContentLoaded", function () {
	console.log("DOMContentLoaded") // Debugging

	document.querySelectorAll(".nw-stepper").forEach(function (stepper) {
		console.log("Stepper:", stepper) // Debugging

		const input = stepper.querySelector(".nw-anzahl-input")
		const upBtn = stepper.querySelector(".nw-step-up")
		const downBtn = stepper.querySelector(".nw-step-down")

		if (!input || !upBtn || !downBtn) {
			console.warn("Fehlende Felder in Stepper:", stepper) // Debugging
			return
		}

		const update = () => {
			// handleManualDeactivation() // zuerst deaktivieren (rekursiv)
			validiereAbhaengigkeiten() // dann Anforderungen checken
			updateRowStates()
			berechneSummenProContainer()
		}

		// Neue Funktion für Dark-Mode basierend auf Anzahl
		function updateRowStates() {
			document.querySelectorAll(".nw-rechner-row").forEach(function (row) {
				const input = row.querySelector(".nw-anzahl-input")
				if (input) {
					const anzahl = parseInt(input.value) || 0
					if (anzahl === 0) {
						row.classList.add("dark")
					} else {
						row.classList.remove("dark")
					}
				}
			})
		}

		// Hilfsfunktion um Button-Status zu aktualisieren
		const updateButtonStates = () => {
			const currentValue = parseInt(input.value) || 0
			const minValue = parseInt(input.dataset.min) || 0
			const maxValue = parseInt(input.dataset.max)

			// Down-Button deaktivieren wenn am Minimum
			downBtn.disabled = currentValue <= minValue

			// Up-Button deaktivieren wenn am Maximum (falls gesetzt)
			if (maxValue && !isNaN(maxValue)) {
				upBtn.disabled = currentValue >= maxValue
			} else {
				upBtn.disabled = false
			}
		}

		// ↓ Down-Button: dekrementieren und dann nur update()
		downBtn.addEventListener("click", () => {
			const minValue = parseInt(input.dataset.min, 10) || 0
			const currentValue = parseInt(input.value, 10) || 0

			if (currentValue > minValue) {
				// 1) runterscrollen
				input.value = currentValue - 1

				// 2) manuelle Deaktivierung markieren
				const row = stepper.closest(".nw-rechner-row")
				row.dataset.userDeactivated = "true"

				// 3) UI und Abhängigkeiten
				updateButtonStates()
				update()
			}
		})

		// ↑ Up-Button: inkrementieren und dann nur update()
		upBtn.addEventListener("click", () => {
			const maxValue = parseInt(input.dataset.max, 10)
			const currentValue = parseInt(input.value, 10) || 0

			if (!maxValue || currentValue < maxValue) {
				// 1) hochzählen
				input.stepUp()

				// 2) User-Flag zurücksetzen
				const row = stepper.closest(".nw-rechner-row")
				const thisSlug = row.dataset.slug
				delete row.dataset.userDeactivated
				removeUserDeactivatedForDependencies(thisSlug)

				// 3) UI und Abhängigkeiten
				updateButtonStates()
				update()
			}
		})

		input.addEventListener("change", () => {
			updateButtonStates()
			update()
		})

		// Initial Button-Status setzen
		updateButtonStates()
	})

	// Initial berechnen
	berechneSummenProContainer()
})
