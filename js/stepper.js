console.log("newwways stepper.js wurde geladen!")

function toSlug(str) {
	return str
		.replace(/ä/g, "ae")
		.replace(/ö/g, "oe")
		.replace(/ü/g, "ue")
		.replace(/Ä/g, "Ae")
		.replace(/Ö/g, "Oe")
		.replace(/Ü/g, "Ue")
		.replace(/ß/g, "ss")
		.toLowerCase()
		.normalize("NFD")
		.replace(/[\u0300-\u036f]/g, "")
		.replace(/[^a-z0-9]+/g, "-")
		.replace(/^-+|-+$/g, "")
}


function removeUserDeactivatedForDependencies(slug) {
	document.querySelectorAll(".nw-rechner-row").forEach((row) => {
		const requires = row.dataset.requires
		const requiredSlug = toSlug(requires)
		if (requiredSlug === slug) {
			delete row.dataset.userDeactivated
		}
	})
}

// Hilfsfunktion um "on-change" Klasse zu setzen und automatisch zu entfernen
function addOnChangeClass(row) {
	if (!row) return
	
	row.classList.add("on-change")
	console.log("Added on-change class to:", row.dataset.slug)
	
	// Nach 2 Sekunden wieder entfernen
	setTimeout(() => {
		row.classList.remove("on-change")
		console.log("Removed on-change class from:", row.dataset.slug)
	}, 333)
}



/* Eigentliche Logik */ 

// Globale Variable um zu tracken welche Row gerade geändert wurde
let lastChangedRow = null

/*
function validiereAbhaengigkeiten() {
	console.log("Validiere Abhängigkeiten (smart bidirektional)")

	document.querySelectorAll(".nw-rechner-container").forEach((container) => {
		const rows = Array.from(container.querySelectorAll(".nw-rechner-row"))

		rows.forEach((row) => {
			const requires = row.dataset.requires
			if (!requires) return // keine Abhängigkeit

			const childSlug = row.dataset.slug
			const childInp = row.querySelector(".nw-anzahl-input")
			const childCnt = parseInt(childInp?.value || 0, 10)
			
			const num = parseInt(row.dataset.requiresNumerator, 10) || 1
			const den = parseInt(row.dataset.requiresDenominator, 10) || 1
			const parentSlug = toSlug(requires)
			
			// Finde Parent-Row
			const parentRow = document.querySelector(`[data-slug="${parentSlug}"]`)
			if (!parentRow) return
			
			const parentInp = parentRow.querySelector(".nw-anzahl-input")
			const parentCnt = parseInt(parentInp?.value || 0, 10)

			// Berechne was Parent maximal unterstützen kann
			const maxSupportedChildren = Math.floor((parentCnt * num) / den)
			
			// Entscheide basierend darauf WER gerade geändert wurde
			if (lastChangedRow === row) {
				// CHILD wurde geändert → nur UPWARD prüfen
				if (childCnt > maxSupportedChildren) {
					const parentNeeded = Math.ceil((childCnt * den) / num)
					console.log(`UPWARD: ${childSlug} (${childCnt}) → ${parentSlug} erhöht auf ${parentNeeded}`)
					parentInp.value = parentNeeded

					// Animation für geänderte Parent-Row
					addOnChangeClass(parentRow)
				}
			} else if (lastChangedRow === parentRow) {
				// PARENT wurde geändert → nur DOWNWARD prüfen
				if (childCnt > maxSupportedChildren) {
					console.log(`DOWNWARD: ${parentSlug} (${parentCnt}) → ${childSlug} reduziert auf ${maxSupportedChildren}`)
					childInp.value = maxSupportedChildren

					// Animation für geänderte Child-Row
					addOnChangeClass(row)
				}
			} else {
				// Fallback: beide Richtungen (für initiale Validierung)
				if (childCnt > maxSupportedChildren) {
					const parentNeeded = Math.ceil((childCnt * den) / num)
					console.log(`FALLBACK: ${childSlug} (${childCnt}) → ${parentSlug} erhöht auf ${parentNeeded}`)
					parentInp.value = parentNeeded

					// Animation für geänderte Parent-Row
					addOnChangeClass(parentRow)
				}
			}
		})
	})
	
	// Reset nach Validierung
	lastChangedRow = null
}
*/

function validiereAbhaengigkeiten() {
	console.log("Validiere Abhängigkeiten (smart bidirektional)")

	document.querySelectorAll(".nw-rechner-container").forEach((container) => {
		const rows = Array.from(container.querySelectorAll(".nw-rechner-row"))

		rows.forEach((row) => {
			const requires = row.dataset.requires
			if (!requires) return // keine Abhängigkeit

			const childSlug = row.dataset.slug
			const childInp = row.querySelector(".nw-anzahl-input")
			const childCnt = parseInt(childInp?.value || 0, 10)

			const num = parseInt(row.dataset.requiresNumerator, 10) || 1
			const den = parseInt(row.dataset.requiresDenominator, 10) || 1
			const parentSlug = toSlug(requires)

			// Finde Parent-Row
			const parentRow = document.querySelector(`[data-slug="${parentSlug}"]`)
			if (!parentRow) return

			const parentInp = parentRow.querySelector(".nw-anzahl-input")
			const parentCnt = parseInt(parentInp?.value || 0, 10)

			// Berechne was Parent maximal UND minimal unterstützen kann
			const maxSupportedChildren = Math.floor((parentCnt * num) / den)
			const minPercentage = 0.4 // 40% Mindestauslastung
			const minRequiredChildren = parentCnt > 0 ? Math.ceil((parentCnt * num * minPercentage) / den) : 0

			// Entscheide basierend darauf WER gerade geändert wurde
			if (lastChangedRow === row) {
				// CHILD wurde geändert → nur UPWARD prüfen
				if (childCnt > maxSupportedChildren) {
					const parentNeeded = Math.ceil((childCnt * den) / num)
					console.log(`UPWARD: ${childSlug} (${childCnt}) → ${parentSlug} erhöht auf ${parentNeeded}`)
					parentInp.value = parentNeeded
					addOnChangeClass(parentRow)
				}
			} else if (lastChangedRow === parentRow) {
				// PARENT wurde geändert → DOWNWARD und UPWARD prüfen
				if (childCnt > maxSupportedChildren) {
					console.log(`DOWNWARD: ${parentSlug} (${parentCnt}) → ${childSlug} reduziert auf ${maxSupportedChildren}`)
					childInp.value = maxSupportedChildren
					addOnChangeClass(row)
				}
				// NEU: Mindestauslastung prüfen
				else if (parentCnt > 0 && childCnt < minRequiredChildren) {
					console.log(`UPWARD MIN: ${parentSlug} (${parentCnt}) → ${childSlug} erhöht auf ${minRequiredChildren}`)
					childInp.value = minRequiredChildren
					addOnChangeClass(row)
				}
			} else {
				// Fallback: beide Richtungen (für initiale Validierung)
				if (childCnt > maxSupportedChildren) {
					const parentNeeded = Math.ceil((childCnt * den) / num)
					console.log(`FALLBACK: ${childSlug} (${childCnt}) → ${parentSlug} erhöht auf ${parentNeeded}`)
					parentInp.value = parentNeeded
					addOnChangeClass(parentRow)
				}
				// NEU: Mindestauslastung im Fallback
				else if (parentCnt > 0 && childCnt < minRequiredChildren) {
					console.log(`FALLBACK MIN: ${parentSlug} (${parentCnt}) → ${childSlug} erhöht auf ${minRequiredChildren}`)
					childInp.value = minRequiredChildren
					addOnChangeClass(row)
				}
			}
		})
	})

	// Reset nach Validierung
	lastChangedRow = null
}

function berechneSummenProContainer() {
	console.log("Berechne Summen pro Container") // Debugging

	document.querySelectorAll(".nw-rechner-container").forEach(function (container) {
		console.log("Container:", container)

		let summe = 0

		// Alle Elemente im Container durchgehen
		const rows = container.querySelectorAll(".nw-rechner-row")
		console.log("Gefundene Zeilen:", rows) // Debugging
		console.log("Zeilen im Container:", rows.length)

		rows.forEach(function (row) {
			const input = row.querySelector(".nw-anzahl-input")
			const preisFeld = row.querySelector(".nw-preis-wert")

			if (!input || !preisFeld) {
				console.warn("Fehlende Felder in Zeile:", row) // Debugging
				return
			}
			console.log("Datensatz:", preisFeld.dataset.preis)
			const preis = parseFloat(preisFeld.dataset.preis)
			const anzahl = parseInt(input.value) || 0

			if (!isNaN(preis) && !isNaN(anzahl)) {
				summe += anzahl * preis
			}
		})

		// Gesamtsumme anzeigen
		const mwst = summe * 0.19
		const brutto = summe + mwst

		const gesamtFeld = container.querySelector(".netto-summe-wert")
		const mwstFeld = container.querySelector(".mwst-summe-wert")
		const bruttoFeld = container.querySelector(".brutto-summe-wert")

		if (gesamtFeld) {
			gesamtFeld.textContent = summe.toFixed(2).replace(".", ",")
		}
		if (mwstFeld) {
			mwstFeld.textContent = mwst.toFixed(2).replace(".", ",")
		}
		if (bruttoFeld) {
			bruttoFeld.textContent = brutto.toFixed(2).replace(".", ",")
		}
	})
}

document.addEventListener("DOMContentLoaded", function () {
	console.log("DOMContentLoaded") // Debugging

	document.querySelectorAll(".nw-stepper").forEach(function (stepper) {
		console.log("Stepper:", stepper) // Debugging

		const input = stepper.querySelector(".nw-anzahl-input")
		const upBtn = stepper.querySelector(".nw-step-up")
		const downBtn = stepper.querySelector(".nw-step-down")

		if (!input || !upBtn || !downBtn) {
			console.warn("Fehlende Felder in Stepper:", stepper) // Debugging
			return
		}

		const update = () => {
			// handleManualDeactivation() // zuerst deaktivieren (rekursiv)
			validiereAbhaengigkeiten() // dann Anforderungen checken
			updateRowStates()
			berechneSummenProContainer()
		}

		// Neue Funktion für Dark-Mode basierend auf Anzahl
		function updateRowStates() {
			document.querySelectorAll(".nw-rechner-row").forEach(function (row) {
				const input = row.querySelector(".nw-anzahl-input")
				if (input) {
					const anzahl = parseInt(input.value) || 0
					if (anzahl === 0) {
						row.classList.add("dark")
					} else {
						row.classList.remove("dark")
					}
				}
			})
		}

		// Hilfsfunktion um Button-Status zu aktualisieren
		// Hilfsfunktion um Button-Status zu aktualisieren
		// const updateButtonStates = () => {
		// 	const currentValue = parseInt(input.value) || 0
		// 	const minValue = parseInt(input.dataset.min) || 0
		// 	const maxValue = parseInt(input.dataset.max)

		// 	// NUR statische HTML-Limits berücksichtigen, KEINE Abhängigkeiten!
		// 	downBtn.disabled = currentValue <= minValue
		// 	upBtn.disabled = maxValue && currentValue >= maxValue
		// }
		// Hilfsfunktion um Button-Status zu aktualisieren
		const updateButtonStates = () => {
			// NICHTS disablen - Buttons sind immer aktiviert!
			downBtn.disabled = false
			upBtn.disabled = false
		}

		// ↓ Down-Button: dekrementieren und dann nur update()
		downBtn.addEventListener("click", () => {
			const minValue = parseInt(input.dataset.min, 10) || 0
			const currentValue = parseInt(input.value, 10) || 0

			if (currentValue > minValue) {
				// 1) runterscrollen
				input.value = currentValue - 1

				// 2) Merken welche Row geändert wurde
				const row = stepper.closest(".nw-rechner-row")
				lastChangedRow = row
				row.dataset.userDeactivated = "true"

				// 3) UI und Abhängigkeiten
				update()
			}
		})

		// ↑ Up-Button: inkrementieren und dann nur update()
		upBtn.addEventListener("click", () => {
			const maxValue = parseInt(input.dataset.max, 10)
			const currentValue = parseInt(input.value, 10) || 0

			if (!maxValue || currentValue < maxValue) {
				// 1) hochzählen
				input.value = currentValue + 1

				// 2) Merken welche Row geändert wurde
				const row = stepper.closest(".nw-rechner-row")
				lastChangedRow = row
				const thisSlug = row.dataset.slug
				delete row.dataset.userDeactivated
				removeUserDeactivatedForDependencies(thisSlug)

				// 3) UI und Abhängigkeiten
				update()
			}
		})

		input.addEventListener("change", (e) => {
			// Wenn das Event nicht vom User (isTrusted=false) stammt, ignorieren
			if (!e.isTrusted) return
			updateButtonStates()
			update()
		})

		// Initial Button-Status setzen
		updateButtonStates()
	})

	// Initial berechnen
	berechneSummenProContainer()
})




/**
 * Lightbox funktionalität
 */

// Lightbox-Funktionalität
document.addEventListener('DOMContentLoaded', function() {
	console.log("dokument loaded -> lightbox") // Debugging

	// Zentrale Reset-Funktion für Lightbox
	function resetLightbox(lightbox) {
		if (!lightbox) return

		lightbox.style.display = "none"
		document.body.style.overflow = ""

		// Form zurücksetzen
		const form = lightbox.querySelector(".nw-contact-form")
		const success = lightbox.querySelector(".nw-form-success")
		const submitButton = form?.querySelector('button[type="submit"]')

		if (form && success) {
			form.style.display = "block"
			success.style.display = "none"
			form.reset()

			// Button-Text und Status zurücksetzen
			if (submitButton) {
				submitButton.disabled = false
				submitButton.textContent = "Anfrage senden"
			}
		}
	}

	// Click-Handler:
	document.addEventListener("click", function (e) {
		console.log("Click-Event:", e.target)

		if (e.target.classList.contains("nw-open-lightbox")) {
			e.preventDefault()
			const containerId = e.target.dataset.container
			console.log("Container ID:", containerId)

			// DEBUG: Alle verfügbaren Lightboxes auflisten
			const allLightboxes = document.querySelectorAll('[id^="nw-lightbox-"]')
			console.log("Alle verfügbaren Lightboxes:", allLightboxes)

			// DEBUG: Alle verfügbaren Container auflisten
			const allContainers = document.querySelectorAll("[data-container-id]")
			console.log("Alle verfügbaren Container:", allContainers)

			const lightbox = document.getElementById("nw-lightbox-" + containerId)
			const container = document.querySelector('[data-container-id="' + containerId + '"]')

			console.log("Gesuchte Lightbox ID:", "nw-lightbox-" + containerId)
			console.log("Gesuchte Container ID:", containerId)
			console.log("Lightbox gefunden:", lightbox)
			console.log("Container gefunden:", container)

			if (lightbox && container) {
				updateSelectionSummary(container, lightbox)
				lightbox.style.display = "flex"
				document.body.style.overflow = "hidden"
				console.log("Lightbox geöffnet!")
			} else {
				console.error("Lightbox oder Container nicht gefunden!")
				console.error(
					"Verfügbare Lightbox IDs:",
					Array.from(allLightboxes).map((l) => l.id)
				)
				console.error(
					"Verfügbare Container IDs:",
					Array.from(allContainers).map((c) => c.dataset.containerId)
				)
			}
		}
	})

	// LIGHTBOX CLOSE HANDLER:
	document.addEventListener("click", function (e) {
		if (e.target.classList.contains("nw-lightbox-close") || e.target.classList.contains("nw-global-lightbox")) {
			const lightbox = e.target.closest(".nw-global-lightbox")
			resetLightbox(lightbox)
		}
	})

	// ESC HANDLER:
	document.addEventListener("keydown", function (e) {
		if (e.key === "Escape") {
			const openLightbox = document.querySelector('.nw-global-lightbox[style*="flex"]')
			resetLightbox(openLightbox)
		}
	})

	// Formular-Submit
	document.addEventListener("submit", function (e) {
		if (e.target.classList.contains("nw-contact-form")) {
			e.preventDefault()
			handleFormSubmit(e.target)
		}
	})
});

// Auswahl-Summary aktualisieren
function updateSelectionSummary(container, lightbox) {
    const summaryDiv = lightbox.querySelector('.nw-selection-summary');
    const rows = container.querySelectorAll('.nw-rechner-row');
    
    let summaryHTML = '';
    let totalNetto = 0;
    let hasSelection = false;
    
    rows.forEach(row => {
        const anzahlInput = row.querySelector('.nw-anzahl-input');
        const anzahl = parseInt(anzahlInput.value) || 0;
        
        if (anzahl > 0) {
            hasSelection = true;
            const anlagetyp = row.querySelector('.nw-anlagetyp-text').textContent;
            const beschreibung = row.querySelector('.nw-beschreibung-text').textContent;
            const preisElement = row.querySelector('.nw-preis-wert');
            const einzelpreis = parseFloat(preisElement.dataset.preis) || 0;
            const gesamtpreis = einzelpreis * anzahl;
            
            totalNetto += gesamtpreis;
            
            summaryHTML += `
                <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                    <span><strong>${anlagetyp}:</strong> ${beschreibung} (${anzahl}x)</span>
                    <span>${gesamtpreis.toLocaleString('de-DE', {minimumFractionDigits: 2})} €</span>
                </div>
            `;
        }
    });
    
    if (!hasSelection) {
        summaryHTML = '<p style="color: #666; font-style: italic;">Keine Leistungen ausgewählt</p>';
    } else {
        const mwst = totalNetto * 0.19;
        const brutto = totalNetto + mwst;
        
        summaryHTML += `
            <hr style="margin: 15px 0; border: none; border-top: 1px solid #ddd;">
            <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                <span>Gesamtkosten (netto):</span>
                <span><strong>${totalNetto.toLocaleString('de-DE', {minimumFractionDigits: 2})} €</strong></span>
            </div>
            <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                <span>zzgl. 19% MwSt.:</span>
                <span>${mwst.toLocaleString('de-DE', {minimumFractionDigits: 2})} €</span>
            </div>
            <div style="display: flex; justify-content: space-between; font-weight: bold; font-size: 1.1em;">
                <span>Gesamtkosten (brutto):</span>
                <span>${brutto.toLocaleString('de-DE', {minimumFractionDigits: 2})} €</span>
            </div>
        `;
    }
    
    summaryDiv.innerHTML = summaryHTML;
}

// Formular-Submit verarbeiten
function handleFormSubmit(form) {
    const submitButton = form.querySelector('button[type="submit"]');
    const originalText = submitButton.textContent;
    
    // Button deaktivieren
    submitButton.disabled = true;
    submitButton.textContent = 'Wird gesendet...';
    
    // Formular-Daten sammeln
    const formData = new FormData(form);
    const lightbox = form.closest(".nw-global-lightbox")
    const containerId = lightbox.id.replace('nw-lightbox-', '');
    const container = document.querySelector('[data-container-id="' + containerId + '"]');
    
    // Auswahl-Daten sammeln
    const selectionData = getSelectionData(container);
    
    // AJAX-Daten vorbereiten
    const ajaxData = {
        action: 'nw_send_contact_form',
        nonce: nw_ajax.nonce,
        name: formData.get('name'),
        email: formData.get('email'),
        phone: formData.get('phone'),
        message: formData.get('message'),
        selection_data: selectionData,
        empfaenger: form.dataset.email
    };
    
    // AJAX-Request
    fetch(nw_ajax.ajax_url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams(ajaxData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Erfolgsmeldung anzeigen
            form.style.display = 'none';
            lightbox.querySelector('.nw-form-success').style.display = 'block';
        } else {
            alert('Fehler: ' + (data.data || 'Unbekannter Fehler'));
            submitButton.disabled = false;
            submitButton.textContent = originalText;
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Ein Fehler ist aufgetreten. Bitte versuchen Sie es später erneut.');
        submitButton.disabled = false;
        submitButton.textContent = originalText;
    });
}

// Auswahl-Daten als Text formatieren
function getSelectionData(container) {
	const rows = container.querySelectorAll(".nw-rechner-row")
	let selectionText = ""
	let totalNetto = 0

	rows.forEach((row) => {
		const anzahlInput = row.querySelector(".nw-anzahl-input")
		const anzahl = parseInt(anzahlInput.value) || 0

		if (anzahl > 0) {
			const anlagetyp = row.querySelector(".nw-anlagetyp-text").textContent
			const beschreibung = row.querySelector(".nw-beschreibung-text").textContent
			const preisElement = row.querySelector(".nw-preis-wert")
			const einzelpreis = parseFloat(preisElement.dataset.preis) || 0
			const gesamtpreis = einzelpreis * anzahl

			totalNetto += gesamtpreis

			selectionText += `\r\n${anlagetyp}: ${beschreibung}\r\n`
			selectionText += `   Anzahl: ${anzahl}\r\n`
			selectionText += `   Einzelpreis: ${einzelpreis.toLocaleString("de-DE", {minimumFractionDigits: 2})} € (netto)\r\n`
			selectionText += `   Gesamtpreis: ${gesamtpreis.toLocaleString("de-DE", {minimumFractionDigits: 2})} € (netto)\r\n`
		}
	})

	if (totalNetto > 0) {
		const mwst = totalNetto * 0.19
		const brutto = totalNetto + mwst

		selectionText += `\r\n=============================\r\n`
		selectionText += `GESAMTKOSTEN:\r\n`
		selectionText += `=============================\r\n`
		selectionText += `Netto: ${totalNetto.toLocaleString("de-DE", {minimumFractionDigits: 2})} €\r\n`
		selectionText += `MwSt. (19%): ${mwst.toLocaleString("de-DE", {minimumFractionDigits: 2})} €\r\n`
		selectionText += `Brutto: ${brutto.toLocaleString("de-DE", {minimumFractionDigits: 2})} €\r\n`
	}

	return selectionText || "Keine Leistungen ausgewählt"
}