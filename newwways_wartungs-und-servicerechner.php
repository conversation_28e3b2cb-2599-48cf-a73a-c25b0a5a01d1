<?php
/**
 * Plugin Name: newwways-wartungs-und-servicerechner Element 
 * Description: WP-Backery Element für ein interaktiven Wartungs- und Servicerechner
 * Version: 1.0
 * Author: <PERSON>
 */

/* Admin Settings */ 
// Admin-<PERSON><PERSON> hinz<PERSON>gen
add_action('admin_menu', 'nw_wartungsrechner_admin_menu');

function nw_wartungsrechner_admin_menu() {
    add_menu_page(
        'Wartungsrechner Einstellungen',     // Seitentitel
        'Wartungsrechner',                   // Menütitel
        'manage_options',                    // Berechtigung
        'wartungsrechner-settings',          // Menü-Slug
        'nw_wartungsrechner_admin_page',     // Callback-Funktion
        'dashicons-calculator',              // Icon
        30                                   // Position
    );
}

// Admin-Seite anzeigen
function nw_wartungsrechner_admin_page() {
    // Einstellungen speichern
    if (isset($_POST['submit']) && check_admin_referer('nw_wartungsrechner_settings', 'nw_nonce')) {
        $email = sanitize_email($_POST['wartungsrechner_email']);
        update_option('nw_wartungsrechner_email', $email);
        
        echo '<div class="notice notice-success is-dismissible"><p>Einstellungen gespeichert!</p></div>';
    }
    
    // Aktuelle E-Mail-Adresse abrufen
    $current_email = get_option('nw_wartungsrechner_email', get_option('admin_email'));
    ?>
    <div class="wrap">
        <h1>Wartungsrechner Einstellungen</h1>
        
        <form method="post" action="">
            <?php wp_nonce_field('nw_wartungsrechner_settings', 'nw_nonce'); ?>
            
            <table class="form-table">
                <tr>
                    <th scope="row">
                        <label for="wartungsrechner_email">E-Mail-Empfänger</label>
                    </th>
                    <td>
                        <input 
                            type="email" 
                            id="wartungsrechner_email" 
                            name="wartungsrechner_email" 
                            value="<?php echo esc_attr($current_email); ?>" 
                            class="regular-text"
                            required
                        />
                        <p class="description">
                            An diese E-Mail-Adresse werden die Anfragen vom Wartungsrechner gesendet.
                        </p>
                    </td>
                </tr>
            </table>
            
            <?php submit_button('Einstellungen speichern'); ?>
        </form>
        
        <hr>
        
        <h2>Plugin-Informationen</h2>
        <table class="form-table">
            <tr>
                <th scope="row">Plugin-Version</th>
                <td>1.1</td>
            </tr>
            <tr>
                <th scope="row">Standard E-Mail</th>
                <td><?php echo esc_html(get_option('admin_email')); ?></td>
            </tr>
            <tr>
                <th scope="row">Aktuelle E-Mail</th>
                <td><strong><?php echo esc_html($current_email); ?></strong></td>
            </tr>
        </table>
        
        <h3>Shortcode</h3>
        <p>Verwenden Sie diesen Shortcode, um den Wartungsrechner einzufügen:</p>
        <code>[newwways_wartungs_und_servicerechner]</code>
    </div>
    <?php
}

// CSS für Admin-Seite
add_action('admin_head', 'nw_wartungsrechner_admin_css');

function nw_wartungsrechner_admin_css() {
    $screen = get_current_screen();
    if ($screen->id === 'toplevel_page_wartungsrechner-settings') {
        ?>
        <style>
            .wrap h1 {
                color: #2c3e50;
            }
            .form-table th {
                width: 200px;
            }
            code {
                background: #f1f1f1;
                padding: 2px 6px;
                border-radius: 3px;
                font-family: Consolas, Monaco, monospace;
            }
        </style>
        <?php
    }
}


// Globale Variable für die lightbox-generierung:
global $nw_rechner_containers;
if (!isset($nw_rechner_containers)) {
    $nw_rechner_containers = array();
}

// 5. NEUE FUNKTION für Footer Lightboxes hinzufügen:
function nw_output_rechner_lightboxes() {
    global $nw_rechner_containers;
    
    if (empty($nw_rechner_containers)) {
        echo '<!-- Keine Rechner-Container gefunden -->';
        return;
    }
    
    echo '<!-- Lightboxes für ' . count($nw_rechner_containers) . ' Container -->';
    
    foreach ($nw_rechner_containers as $container) {
        $container_id = $container['id'];
        $email = $container['email'];
        echo '<!-- Lightbox für Container: ' . $container_id . ' -->';
        ?>
        <div id="nw-lightbox-<?php echo esc_attr($container_id); ?>" class="nw-global-lightbox" style="display: none;">
            <div class="nw-lightbox-content">
                <div class="nw-lightbox-header">
                    <h3>Unverbindliche Anfrage</h3>
                    <button type="button" class="nw-lightbox-close">&times;</button>
                </div>
                
                <form class="nw-contact-form" data-email="<?php echo esc_attr($email); ?>">
                    <div class="nw-form-group">
                        <label for="nw-name-<?php echo esc_attr($container_id); ?>">Name *</label>
                        <input type="text" id="nw-name-<?php echo esc_attr($container_id); ?>" name="name" required>
                    </div>
                    
                    <div class="nw-form-group">
                        <label for="nw-phone-<?php echo esc_attr($container_id); ?>">Telefon</label>
                        <input type="tel" id="nw-phone-<?php echo esc_attr($container_id); ?>" name="phone">
                    </div>
                    
                    <div class="nw-form-group">
                        <label for="nw-email-<?php echo esc_attr($container_id); ?>">E-Mail *</label>
                        <input type="email" id="nw-email-<?php echo esc_attr($container_id); ?>" name="email" required>
                    </div>
                    
                    <div class="nw-form-group">
                        <label for="nw-message-<?php echo esc_attr($container_id); ?>">Zusätzliche Informationen</label>
                        <textarea id="nw-message-<?php echo esc_attr($container_id); ?>" name="message" rows="4"></textarea>
                    </div>
                    
                    <div class="nw-form-group">
                        <h4>Ihre Auswahl:</h4>
                        <div class="nw-selection-summary"></div>
                    </div>
                    
                    <div class="nw-form-actions">
                        <button type="submit" class="button button-primary">Anfrage senden</button>
                    </div>
                </form>
                
                <div class="nw-form-success" style="display: none;">
                    <h4>Vielen Dank!</h4>
                    <p>Ihre Anfrage wurde erfolgreich gesendet. Wir melden uns zeitnah bei Ihnen.</p>
                    <!-- <button type="button" class="button button-primary nw-lightbox-close">Schließen</button> -->
                </div>
            </div>
        </div>
        <?php
    }
}

// 6. HOOK für Footer hinzufügen:
add_action('wp_footer', 'nw_output_rechner_lightboxes');





// Verhindere doppelte Ausführung
if ( ! function_exists( 'newwways_wartungs_und_servicerechner_vc_element' ) ) {

    // Registriere das Element bei WPBakery
    add_action('vc_before_init', 'newwways_wartungs_und_servicerechner_vc_element');

    function newwways_wartungs_und_servicerechner_vc_element() {
        vc_map([
            'name' => __('Wartungs- und Servicerechner', 'textdomain'),
            'base' => 'newwways_wartungs_und_servicerechner',
            'description' => __('Interaktiver Rechner für Wartungs- und Serviceleistungen.', 'textdomain'),
            'category' => __('Eigene Elemente', 'textdomain'),
            'params' => [
                // Kopfzeilen-Felder
                [
                    'type' => 'textfield',
                    'heading' => __('Kopfzeile: Anlagetyp', 'textdomain'),
                    'param_name' => 'header_anlagetyp',
                    'description' => __('Überschrift für die erste Spalte (z.B. "Gerätetyp" oder "Luftvolumenstrom")', 'textdomain'),
                    'value' => 'Anlagetyp',
                    'group' => __('Tabellenüberschriften', 'textdomain'),
                ],
                [
                    'type' => 'textfield',
                    'heading' => __('Kopfzeile: Beschreibung', 'textdomain'),
                    'param_name' => 'header_beschreibung',
                    'description' => __('Überschrift für die Beschreibungs-Spalte', 'textdomain'),
                    'value' => 'Beschreibung',
                    'group' => __('Tabellenüberschriften', 'textdomain'),
                ],
                [
                    'type' => 'textfield',
                    'heading' => __('Kopfzeile: Anzahl', 'textdomain'),
                    'param_name' => 'header_anzahl',
                    'description' => __('Überschrift für die Anzahl-Spalte', 'textdomain'),
                    'value' => 'Anzahl',
                    'group' => __('Tabellenüberschriften', 'textdomain'),
                ],
                [
                    'type' => 'textfield',
                    'heading' => __('Kopfzeile: Preis', 'textdomain'),
                    'param_name' => 'header_preis',
                    'description' => __('Überschrift für die Preis-Spalte', 'textdomain'),
                    'value' => 'Preis',
                    'group' => __('Tabellenüberschriften', 'textdomain'),
                ],
                [
                    'type' => 'param_group',
                    'heading' => __('Rechner-Einträge', 'textdomain'),
                    'param_name' => 'rechner_eintraege',
                    'description' => __('Füge hier die einzelnen Einträge für den Rechner hinzu.', 'textdomain'),
                    'params' => [
                        [
                            'type' => 'textfield',
                            'heading' => __('Anlagetyp', 'textdomain'),
                            'param_name' => 'feld_anlagetyp',
                            'description' => __('Der Typ der Anlage/des Geräts', 'textdomain'),
                            'admin_label' => true,
                        ],
                        [
                            'type' => 'textfield',
                            'heading' => __('Text eingeben', 'textdomain'),
                            'param_name' => 'feld_main_text',
                            'description' => __('Der Text für das Element', 'textdomain'),
                        ],
                        [
                            'type' => 'textfield',
                            'heading' => __('Info Text', 'textdomain'),
                            'param_name' => 'feld_text',
                            'description' => __('Für i-Button, erscheint wenn man über den i-Button hovert.', 'textdomain'),
                        ],
                        [
                            'type' => 'textarea',
                            'heading' => __('Beispiele', 'textdomain'),
                            'param_name' => 'feld_beispiele',
                            'description' => __('Beispiele für diesen Service/diese Leistung.', 'textdomain'),
                        ],
                        [
                            'type' => 'textfield',
                            'heading' => __('Default Count', 'textdomain'),
                            'param_name' => 'feld_default_count',
                            'description' => __('Standard-Anzahl beim Laden', 'textdomain'),
                            'value' => '0',
                        ],
                        [
                            'type' => 'textfield',
                            'heading' => __('Min Count', 'textdomain'),
                            'param_name' => 'feld_min_count',
                            'description' => __('Minimale Anzahl', 'textdomain'),
                            'value' => '0',
                        ],
                        [
                            'type' => 'textfield',
                            'heading' => __('Max Count', 'textdomain'),
                            'param_name' => 'feld_max_count',
                            'description' => __('Maximale Anzahl (leer = unbegrenzt)', 'textdomain'),
                            'value' => '',
                        ],
                        [
                            'type' => 'textfield',
                            'heading' => __('Abhängigkeit (Slug)', 'textdomain'),
                            'param_name' => 'requires_slug',
                            'description' => __('Slug eines anderen Eintrags, der mindestens 1x vorhanden sein muss (z. B. "aussengeraet")', 'textdomain'),
                        ],      
                        
                        [
                            'type' => 'textfield',
                            'heading' => __('Abhängigkeits-Verhältnis Zähler (z. B. 5 für 5:1)', 'textdomain'),
                            'param_name' => 'requires_ratio_numerator',
                            'description' => __('Wie viele Einheiten dürfen pro abhängiger Einheit vorkommen? (z. B. 5 bedeutet: 5 Innengeräte pro 1 Außengerät)', 'textdomain'),
                        ],
                        [
                            'type' => 'textfield',
                            'heading' => __('Abhängigkeits-Verhältnis Nenner (z. B. 1 für 5:1)', 'textdomain'),
                            'param_name' => 'requires_ratio_denominator',
                            'description' => __('Wie viele abhängige Einheiten stehen dem Zähler gegenüber? (z. B. 1 bedeutet: 5 Innengeräte pro 1 Außengerät)', 'textdomain'),
                        ],
                        
                        
                        [
                            'type' => 'textfield',
                            'heading' => __('Mindestanzahl für Abhängigkeit', 'textdomain'),
                            'param_name' => 'requires_min_count',
                            'description' => __('Zahl, wie oft das andere Element mindestens vorkommen muss (Standard: 1)', 'textdomain'),
                            'value' => '1',
                        ],

                        [
                            'type' => 'textfield',
                            'heading' => __('Preis (€)', 'textdomain'),
                            'param_name' => 'feld_preis',
                            'description' => __('Nur ganze Zahlen', 'textdomain'),
                            'value' => '0',
                        ],

                    ],
                ],
                array(
                    'type' => 'textarea',
                    'heading' => 'Zusatzinfo (Fußzeile)',
                    'param_name' => 'footer_note',
                    'description' => 'Optionaler Text unter der Tabelle, z. B. MwSt.-Hinweis oder Kontaktinfo',
                ),
            ],
        ]);
    }

} // Ende vc_element function_exists Check

// Shortcode-Ausgabe - nur wenn noch nicht existiert
if ( ! function_exists( 'newwways_wartungs_und_servicerechner_render' ) ) {
    
    add_shortcode('newwways_wartungs_und_servicerechner', 'newwways_wartungs_und_servicerechner_render');

    function newwways_wartungs_und_servicerechner_render($atts) {
        $atts = shortcode_atts([
            'rechner_eintraege' => '',
            'header_anlagetyp' => 'Anlagetyp',
            'header_beschreibung' => 'Beschreibung', 
            'header_anzahl' => 'Anzahl',
            'header_preis' => 'Preis',
            'footer_note' => '',
        ], $atts);

        // Parse die param_group Daten
        $eintraege = [];
        if (!empty($atts['rechner_eintraege'])) {
            $eintraege = vc_param_group_parse_atts($atts['rechner_eintraege']);
        }

        if (empty($eintraege)) {
            return '<p>' . __('Keine Einträge vorhanden.', 'textdomain') . '</p>';
        }

        $container_id = uniqid('rechner_');

        // DIESE ZEILEN HIER HINZUFÜGEN (INNERHALB der render function):
        global $nw_rechner_containers;
        $nw_rechner_containers[] = array(
            'id' => $container_id,
            'email' => get_option('nw_wartungsrechner_email', get_option('admin_email')),
            'button_text' => 'Jetzt unverbindlich anfragen'
        );

        // Debug-Zeile:
        error_log('NW Debug - Container erstellt: ' . $container_id);

        ob_start();
        ?>
        <div class="nw-rechner-container" data-container-id="<?= esc_attr($container_id); ?>">
            
            <div class="nw-rechner-table">
                <!-- Tabellenheader -->
                <div class="nw-rechner-header">
                    <div class="nw-col-anlagetyp"><?= esc_html($atts['header_anlagetyp']); ?></div>
                    <div class="nw-col-beschreibung"><?= esc_html($atts['header_beschreibung']); ?></div>
                    <!-- <div class="nw-col-icons"></div> -->
                    <div class="nw-col-anzahl"><?= esc_html($atts['header_anzahl']); ?></div>
                    <div class="nw-col-preis"><?= esc_html($atts['header_preis']); ?></div>
                </div>

                <!-- Tabelleninhalt -->
                <div class="nw-rechner-content">
                    <?php foreach ($eintraege as $eintrag): ?>
                        <?php
                        $anlagetyp = isset($eintrag['feld_anlagetyp']) ? $eintrag['feld_anlagetyp'] : '';
                        $main_text = isset($eintrag['feld_main_text']) ? $eintrag['feld_main_text'] : '';
                        $info_text = isset($eintrag['feld_text']) ? $eintrag['feld_text'] : '';
                        $beispiele = isset($eintrag['feld_beispiele']) ? $eintrag['feld_beispiele'] : '';
                        
                        $default_count = intval(isset($eintrag['feld_default_count']) ? $eintrag['feld_default_count'] : 0);
                        $min_count = intval(isset($eintrag['feld_min_count']) ? $eintrag['feld_min_count'] : 0);
                        $max_count_raw = isset($eintrag['feld_max_count']) ? trim($eintrag['feld_max_count']) : '';
                        $max_count = (!empty($max_count_raw) && is_numeric($max_count_raw)) ? intval($max_count_raw) : null;

                        $data_slug = sanitize_title($anlagetyp . '-' . $main_text);

                        $ratio_numerator = isset($eintrag['requires_ratio_numerator']) && is_numeric($eintrag['requires_ratio_numerator']) 
                            ? intval($eintrag['requires_ratio_numerator']) 
                            : null;

                        $ratio_denominator = isset($eintrag['requires_ratio_denominator']) && is_numeric($eintrag['requires_ratio_denominator']) 
                            ? intval($eintrag['requires_ratio_denominator']) 
                            : null;
                        
                        // Sicherstellen, dass default_count innerhalb der Min/Max-Grenzen liegt
                        if ($default_count < $min_count) {
                            $default_count = $min_count;
                        }
                        if ($max_count !== null && $default_count > $max_count) {
                            $default_count = $max_count;
                        }
                        
                        $preis_roh = trim(isset($eintrag['feld_preis']) ? $eintrag['feld_preis'] : '0');
                        $preis_clean = str_replace(['.', ','], ['', '.'], $preis_roh);
                        $preis = is_numeric($preis_clean) ? floatval($preis_clean) : 0;
                        ?>
                        <div 
                            class="nw-rechner-row<?= $default_count == 0 ? ' dark' : ''; ?>"
                            data-slug="<?= esc_attr($data_slug); ?>"
                            data-requires="<?= esc_attr($eintrag['requires_slug'] ?? ''); ?>"
                            data-requires-min="<?= esc_attr($eintrag['requires_min_count'] ?? '1'); ?>"
                            <?= $ratio_numerator !== null ? 'data-requires-numerator="' . esc_attr($ratio_numerator) . '"' : ''; ?>
                            <?= $ratio_denominator !== null ? 'data-requires-denominator="' . esc_attr($ratio_denominator) . '"' : ''; ?>
                        >
                            <div class="nw-col-anlagetyp">
                                <span class="nw-anlagetyp-text"><?= esc_html($anlagetyp); ?></span>
                            </div>
                            
                            <div class="nw-col-beschreibung">
                                <span class="nw-beschreibung-text"><?= esc_html($main_text); ?></span>    
                                <?php if (!empty($info_text) || !empty($beispiele)): ?>
                                    <div class="nw-info-container">
                                        <svg class="nw-info-icon" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" viewBox="0 0 24 24">
                                            <path fill-rule="evenodd" d="M2 12C2 6.477 6.477 2 12 2s10 4.477 10 10-4.477 10-10 10S2 17.523 2 12Zm9.408-5.5a1 1 0 1 0 0 2h.01a1 1 0 1 0 0-2h-.01ZM10 10a1 1 0 1 0 0 2h1v3h-1a1 1 0 1 0 0 2h4a1 1 0 1 0 0-2h-1v-4a1 1 0 0 0-1-1h-2Z" clip-rule="evenodd"/>
                                        </svg>
                                        <div class="nw-info-tooltip">
                                            <?php if (!empty($info_text)): ?>
                                                <div class="nw-tooltip-section">
                                                    <strong>Info:</strong>
                                                    <p><?= wp_kses($info_text, ['br' => [], 'strong' => [], 'em' => [], 'b' => [], 'i' => []]); ?></p>
                                                </div>
                                            <?php endif; ?>
                                            <?php if (!empty($beispiele)): ?>
                                                <div class="nw-tooltip-section">
                                                    <strong>Beispiele:</strong>
                                                    <p><?= wp_kses($beispiele, ['br' => [], 'ul' => [], 'li' => [], 'strong' => [], 'em' => []]); ?></p>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                <?php endif; ?>                            
                            </div>
                            
                            <!-- <div class="nw-col-icons">
                                <?php if (!empty($info_text)): ?>
                                    <span class="nw-info-icon" title="<?= esc_attr($info_text); ?>">ℹ</span>
                                <?php endif; ?>
                                <?php if (!empty($beispiele)): ?>
                                    <span class="nw-beispiel-icon" title="<?= esc_attr($beispiele); ?>">💡</span>
                                <?php endif; ?>
                            </div> -->

                            <!-- <div class="nw-col-icons">
                                
                            </div> -->
                            
                            <div class="nw-col-anzahl">
                                <div class="nw-stepper">
                                    <button type="button" class="nw-step-btn nw-step-down">−</button>
                                    <input type="number" name="anzahl"
                                        class="nw-anzahl-input" 
                                        value="<?= esc_attr($default_count); ?>" 
                                        min="<?= esc_attr($min_count); ?>"
                                        <?php if ($max_count !== null): ?>max="<?= esc_attr($max_count); ?>"<?php endif; ?>
                                        data-min="<?= esc_attr($min_count); ?>"
                                        data-max="<?= $max_count !== null ? esc_attr($max_count) : ''; ?>" />
                                    <button type="button" class="nw-step-btn nw-step-up">+</button>
                                </div>
                            </div>
                            
                            <div class="nw-col-preis">
                                <span class="nw-preis-wert" data-preis="<?= esc_attr($preis); ?>">
                                    <?= number_format($preis, 2, ',', '') ?> €
                                </span>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
                
                <!-- Gesamtsumme -->
                <div class="nw-rechner-summe">
                    <div class="nw-summe-row nw-summe-netto">
                        <span class="nw-summe-label">Gesamtkosten (netto):</span>
                        <span class="nw-summe-wert"><span class="netto-summe-wert">0,00</span> €</span>
                    </div>
                    <div class="nw-summe-row nw-summe-mwst">
                        <span class="nw-summe-label">zzgl. 19% MwSt.:</span>
                        <span class="nw-summe-wert"><span class="mwst-summe-wert">0,00</span> €</span>
                    </div>
                    <div class="nw-summe-row nw-summe-brutto">
                        <span class="nw-summe-label">Gesamtkosten (brutto):</span>
                        <span class="nw-summe-wert"><span class="brutto-summe-wert">0,00</span> €</span>
                    </div>
                </div>

                <!-- <div class="cta-wrapper">
                    <a href="#" class="button button-primary">Jetzt unverbindlich anfragen</a>
                </div> -->

                <?php if (!empty($atts['footer_note'])) : ?>
                    <div class="service-table-footer-note">
                        <?php echo wp_kses_post($atts['footer_note']); ?>
                    </div>
                <?php endif; ?>

                <div class="cta-wrapper">
                    <button type="button" class="button button-primary nw-open-lightbox" data-container="<?= esc_attr($container_id); ?>">
                        Jetzt unverbindlich anfragen
                    </button>
                </div>
            </div>
            
        </div>

        <style>
            .nw-rechner-container .cta-wrapper {
                padding: 0 20px 20px 20px;
                text-align: right;
            }

            button.button.button-primary {
                display: inline-block;
                padding: 0.4rem 1.2rem;
                background-color: #008cea;
                border: solid 2px #008cea;
                color: white;
                text-decoration: none;
                transition: background-color 0.3s ease;
                border-radius: 0;
                cursor: pointer;
            }

            .nw-rechner-container .service-table-footer-note {
                padding: 0 20px 0 20px;
                line-height: 1.5;
                margin: 0 0 1rem 0;
                font-size: 0.85rem;
                color: #6c757d;
                text-align: left;
            }

            .nw-rechner-container .cta-wrapper .button.button-primary {
                display: inline-block;
                padding: 0.4rem 1.2rem;
                background-color: #008cea;
                border: solid 2px #008cea;
                color: white;
                text-decoration: none;
                transition: background-color 0.3s ease, color 0.3s ease;
            }

            .nw-rechner-container .cta-wrapper .button.button-primary:hover {
                display: inline-block;
                padding: 0.4rem 1.2rem;
                background-color: transparent;
                border: solid 2px #008cea;
                color: #008cea;
                text-decoration: none;
            }


            

            /* Optimizing Tabs */
            li.vc_tta-tab.vc_active span.vc_tta-title-text {
                color: #008dea;
            }
            span.vc_tta-title-text {
                font-weight: 600;
            }
            /* End Tabs */

            .vc_tta-panel-body:has(.nw-rechner-container) {
                padding: 0 !important;
            }
            /* Wartungs- und Servicerechner Styles */
            .nw-rechner-container {
                max-width: 100%;
                /* margin: 20px 0; */
                font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
                background: #fff;
                /* border-radius: 8px; */
                /* box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1); */
                overflow: hidden;
                background: linear-gradient(135deg, #f8f9fa 0%, #f4f4f4 100%);
                border-radius: 6px;
            }

            /* rechner table */
            .nw-rechner-table {
                padding: 0;
                width: 100%;
            }

            /* Header Styles */
            .nw-rechner-header {
                display: grid;
                grid-template-columns: 1fr 2fr 120px 100px;
                gap: 15px;
                padding: 16px 20px;
                /* background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); */
                border-bottom: 2px solid #dee2e6;
                font-weight: 600;
                font-size: 14px;
                color: #495057;
                text-transform: uppercase;
                letter-spacing: 0.5px;
            }

            /* Content Rows */
            /* .nw-rechner-content {
                background: #fff;
            } */

            .nw-rechner-row {
                display: grid;
                grid-template-columns: 1fr 2fr 120px 100px;
                gap: 15px;
                padding: 16px 20px;
                border-bottom: 1px solid #e9ecef;
                align-items: center;
                transition: background-color 0.2s ease;
            }

            .nw-rechner-row:hover {
                background-color: #ffffff;
            }

            .nw-rechner-row:last-child {
                border-bottom: none;
            }

            /* Column Styles */
            .nw-col-anlagetyp {
                font-weight: 500;
                color: #212529;
            }

            .nw-col-beschreibung {
                color: #6c757d;
                line-height: 1.4;
                display: flex;
                align-items: center;
                gap: 0.5rem;
                justify-content: start;
            }

            .nw-col-icons {
                display: flex;
                gap: 8px;
                justify-content: center;
            }

            .nw-col-anzahl {
                display: flex;
                justify-content: center;
            }

            .nw-col-preis {
                text-align: right;
                font-weight: 500;
                color: #212529;
            }

            /* Info Icons */
            .nw-info-icon,
            .nw-beispiel-icon {
                display: inline-block;
                width: 20px;
                height: 20px;
                line-height: 20px;
                text-align: center;
                border-radius: 50%;
                font-size: 12px;
                cursor: help;
                transition: all 0.2s ease;
            }

            .nw-info-icon {
                background: #e3f2fd;
                color: #008cea;
                border: 1px solid #bbdefb;
            }

            .nw-info-icon:hover {
                background: #008cea;
                color: white;
                transform: scale(1.1);
            }

            /* Info Icons und Tooltip */
            .nw-info-container {
                position: relative;
                display: inline-block;
            }

            .nw-info-icon {
                width: 20px;
                height: 20px;
                color: #008cea;
                cursor: pointer;
                transition: all 0.2s ease;
                display: block;
            }

            .nw-info-icon:hover {
                color: #fff;
                transform: scale(1.1);
            }

            .nw-info-tooltip {
                position: absolute;
                top: 100%;
                left: 50%;
                transform: translateX(-50%);
                margin-top: 8px;
                background: #fff;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
                padding: 12px 16px;
                min-width: 200px;
                max-width: 300px;
                z-index: 1000;
                opacity: 0;
                visibility: hidden;
                transition: all 0.3s ease;
                font-size: 13px;
                line-height: 1.4;
            }

            .nw-info-tooltip::before {
                content: '';
                position: absolute;
                top: -6px;
                left: 50%;
                transform: translateX(-50%);
                width: 0;
                height: 0;
                border-left: 6px solid transparent;
                border-right: 6px solid transparent;
                border-bottom: 6px solid #fff;
            }

            .nw-info-tooltip::after {
                content: '';
                position: absolute;
                top: -7px;
                left: 50%;
                transform: translateX(-50%);
                width: 0;
                height: 0;
                border-left: 7px solid transparent;
                border-right: 7px solid transparent;
                border-bottom: 7px solid #dee2e6;
            }

            .nw-info-container:hover .nw-info-tooltip {
                opacity: 1;
                visibility: visible;
                transform: translateX(-50%) translateY(0);
            }

            .nw-tooltip-section {
                margin-bottom: 8px;
            }

            .nw-tooltip-section:last-child {
                margin-bottom: 0;
            }

            .nw-tooltip-section strong {
                color: #495057;
                font-weight: 600;
                display: block;
                margin-bottom: 4px;
            }

            .nw-tooltip-section p {
                margin: 0;
                color: #6c757d;
                line-height: 1.4;
            }

            /* Stepper Controls */
            .nw-stepper {
                display: flex;
                align-items: center;
                gap: 2px;
                background: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 6px;
                overflow: hidden;
                max-width: 120px;
                transition: border-color 0.33s ease;
                border: solid 2px transparent;
            }

            .nw-rechner-row.on-change .nw-stepper {
                border: solid 2px #008dea;
            }
            .nw-rechner-row input.nw-anzahl-input {
                transition: color 0.33s ease;
                min-height: 52px !important;
            }
            .nw-rechner-row.on-change input.nw-anzahl-input {
                color: #008dea;
            }

            .nw-step-btn {
                width: 32px;
                height: 52px;
                box-sizing: border-box;
                border: none;
                background: #fff;
                color: #6c757d;
                font-size: 16px;
                font-weight: 500;
                cursor: pointer;
                transition: all 0.2s ease;
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 0.5rem;
            }

            .nw-step-btn:hover {
                background: #e9ecef;
                color: #495057;
            }

            .nw-step-btn:active {
                background: #dee2e6;
                transform: scale(0.95);
            }

            .nw-step-btn:disabled {
                background: #f8f9fa;
                color: #adb5bd;
                cursor: not-allowed;
            }

            .nw-anzahl-input {
                width: 56px;
                height: 36px;
                border: none;
                background: #fff;
                text-align: center;
                font-size: 14px;
                font-weight: 500;
                color: #212529;
                outline: none;
                -moz-appearance: textfield;
            }

            input.nw-anzahl-input {
                color: #333;
                border: none;
                border-radius: 3px;
                height: 100%;
                padding: 0;
            }

            .nw-anzahl-input::-webkit-outer-spin-button,
            .nw-anzahl-input::-webkit-inner-spin-button {
                -webkit-appearance: none;
                margin: 0;
            }

            .nw-anzahl-input:focus {
                background: #f8f9fa;
            }

            /* Preis Styles */
            .nw-preis-wert {
                font-size: 15px;
                color: #008cea;
                font-weight: 600;
            }

            .nw-rechner-row.dark .nw-preis-wert {
                color: #333333;
            }

            /* Summen Bereich */
            .nw-rechner-summe {
                /* background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); */
                border-top: 2px solid #dee2e6;
                padding: 20px 20px 20px 20px;
            }

            .nw-summe-row {
                display: flex;
                justify-content: space-between;
                align-items: center;
            }

            .nw-summe-row:last-child {
                margin-bottom: 0;
                border-top: 2px solid #008cea;
                padding-top: 12px;
                margin-top: 12px;
            }

            .nw-summe-label {
                font-size: 14px;
                color: #6c757d;
            }

            .nw-summe-brutto .nw-summe-label {
                font-weight: 600;
                color: #212529;
                font-size: 16px;
            }

            .nw-summe-wert {
                font-size: 14px;
                font-weight: 500;
                color: #333;
            }

            .nw-summe-brutto .nw-summe-wert {
                font-size: 18px;
                font-weight: 700;
                color: #008cea;
            }

            /* Responsive Design */
            @media (max-width: 999px) {
                .nw-rechner-header,
                .nw-rechner-row {
                    grid-template-columns: 1fr;
                    gap: 10px;
                    text-align: left;
                }
                
                .nw-rechner-header {
                    display: none; /* Header auf mobil verstecken */
                }
                
                .nw-rechner-row {
                    display: block;
                    padding: 16px;
                    /* border-radius: 8px; */
                    /* margin-bottom: 10px; */
                } 

                .nw-rechner-row:hover {
                    background: #f8f9fa;
                }

                .nw-col-anlagetyp,
                .nw-col-beschreibung {
                    margin-bottom: 8px;
                }
                
                .nw-col-anlagetyp {
                    font-size: 16px;
                    font-weight: 600;
                }
                
                .nw-col-icons,
                .nw-col-anzahl,
                .nw-col-preis {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 8px;
                }

                .nw-col-icons::before { content: "Info: "; font-weight: 500; }
                .nw-col-anzahl::before { content: "Anzahl: "; font-weight: 500; }
                .nw-col-preis::before { content: "Preis: "; font-weight: 500; }
                
                .nw-stepper {
                    margin-left: auto;
                }
            }

            @media (max-width: 480px) {
                .nw-rechner-container {
                    margin: 10px 0;
                    border-radius: 6px;
                }
                
                .nw-rechner-row {
                    padding: 12px;
                }
                
                .nw-rechner-summe {
                    padding: 16px;
                }
            }
            </style>
        <?php
        return ob_get_clean();
    }

} // Ende Shortcode function_exists Check


// 4. AJAX HANDLER hinzufügen (nach der render function, vor dem Assets-Teil):
// AJAX Handler für Formular-Versand
add_action('wp_ajax_nw_send_contact_form', 'nw_handle_maintenance_contact_form');
add_action('wp_ajax_nopriv_nw_send_contact_form', 'nw_handle_maintenance_contact_form');

function nw_handle_maintenance_contact_form() {
    // Nonce-Überprüfung
    if (!wp_verify_nonce($_POST['nonce'], 'nw_contact_form_nonce')) {
        wp_die('Sicherheitscheck fehlgeschlagen');
    }
    
    // Eingaben sanitizen
    $name = sanitize_text_field($_POST['name']);
    $email = sanitize_email($_POST['email']);
    $phone = sanitize_text_field($_POST['phone']);
    $message = sanitize_textarea_field($_POST['message']);
    $selection_data = wp_kses($_POST['selection_data'], array());
    $empfaenger = sanitize_email($_POST['empfaenger']);
    
    // Validierung
    if (empty($name) || empty($email) || !is_email($email)) {
        wp_send_json_error('Bitte füllen Sie alle Pflichtfelder korrekt aus.');
        return;
    }
    
    // E-Mail-Inhalt erstellen (HTML-Version)
    $subject = 'Neue Anfrage vom Wartungsrechner';
    $email_body = "<html><body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333;'>";
    $email_body .= "<div style='max-width: 600px; margin: 0 auto; padding: 20px;'>";
    
    $email_body .= "<h2 style='color: #2c3e50; border-bottom: 2px solid #3498db; padding-bottom: 10px;'>Neue Anfrage über den Wartungsrechner</h2>";
    
    $email_body .= "<h3 style='color: #2980b9; margin-top: 30px;'>Kontaktdaten:</h3>";
    $email_body .= "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 20px;'>";
    $email_body .= "<p style='margin: 5px 0;'><strong>Name:</strong> " . esc_html($name) . "</p>";
    $email_body .= "<p style='margin: 5px 0;'><strong>E-Mail:</strong> <a href='mailto:" . esc_attr($email) . "'>" . esc_html($email) . "</a></p>";
    $email_body .= "<p style='margin: 5px 0;'><strong>Telefon:</strong> " . esc_html($phone) . "</p>";
    $email_body .= "</div>";

    if (!empty($message)) {
        $email_body .= "<h3 style='color: #2980b9; margin-top: 30px;'>Nachricht:</h3>";
        $email_body .= "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 20px;'>";
        $email_body .= "<p>" . nl2br(esc_html($message)) . "</p>";
        $email_body .= "</div>";
    }

    $email_body .= "<h3 style='color: #2980b9; margin-top: 30px;'>Ausgewählte Leistungen:</h3>";
    $email_body .= "<div style='background: #f5f5f5; padding: 20px; border-radius: 5px; margin-bottom: 20px; border-left: 4px solid #3498db;'>";
    $email_body .= "<pre style='font-family: Consolas, Monaco, monospace; font-size: 13px; line-height: 1.4; margin: 0; white-space: pre-wrap;'>";
    $email_body .= nl2br(esc_html($selection_data));
    $email_body .= "</pre>";
    $email_body .= "</div>";

    $email_body .= "<hr style='border: none; border-top: 1px solid #ddd; margin: 30px 0;'>";
    $email_body .= "<p style='font-size: 12px; color: #666; text-align: center;'>";
    $email_body .= "Gesendet am: " . current_time('d.m.Y H:i') . " | ";
    $email_body .= "Website: <a href='" . esc_url(home_url()) . "'>" . esc_html(get_bloginfo('name')) . "</a>";
    $email_body .= "</p>";
    
    $email_body .= "</div></body></html>";
    
    // E-Mail senden
    $headers = array('Content-Type: text/html; charset=UTF-8');
    $sent = wp_mail($empfaenger, $subject, $email_body, $headers);
    
    if ($sent) {
        wp_send_json_success('E-Mail erfolgreich gesendet');
    } else {
        wp_send_json_error('Fehler beim Senden der E-Mail');
    }
}



// Load assets

if ( ! function_exists( 'newwways_vc_plugin_assets' ) ) {
    
    add_action('wp_enqueue_scripts', 'newwways_vc_plugin_assets');

    function newwways_vc_plugin_assets() {
        wp_enqueue_script(
            'newwways-vc-stepper',
            plugin_dir_url(__FILE__) . 'js/stepper.js',
            ['jquery'],
            filemtime(plugin_dir_path(__FILE__) . 'js/stepper.js'),
            true
        );
        
        // AJAX-Daten für JavaScript bereitstellen
        wp_localize_script('newwways-vc-stepper', 'nw_ajax', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('nw_contact_form_nonce')
        ));
        
        // CSS für Lightbox hinzufügen
        wp_add_inline_style('wp-block-library', '
            .nw-global-lightbox {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.8);
                z-index: 999999; /* Höher als vorher */
                display: flex;
                align-items: center;
                justify-content: center;
            }
            
            .nw-lightbox-content {
                background: white;
                border-radius: 8px;
                padding: 0;
                max-width: 600px;
                width: 90%;
                max-height: 90vh;
                overflow-y: auto;
                position: relative;
                box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            }
            
            .nw-lightbox-header {
                padding: 20px 30px;
                border-bottom: 1px solid #eee;
                display: flex;
                justify-content: space-between;
                align-items: center;
                background: #f8f9fa;
                border-radius: 8px 8px 0 0;
            }
            
            .nw-lightbox-header h3 {
                margin: 0;
                font-size: 1.5em;
                color: #333;
            }
            
            .nw-lightbox-close {
                background: none;
                border: none;
                font-size: 24px;
                cursor: pointer;
                padding: 0;
                width: 30px;
                height: 30px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 50%;
                transition: background 0.2s;
            }
            
            .nw-lightbox-close:hover {
                background: rgba(0, 0, 0, 0.1);
            }
            
            .nw-contact-form {
                padding: 30px;
            }
            
            .nw-form-group {
                margin-bottom: 20px;
            }
            
            .nw-form-group label {
                display: block;
                margin-bottom: 5px;
                font-weight: bold;
                color: #333;
            }
            
            .nw-form-group input,
            .nw-form-group textarea {
                width: 100%;
                padding: 12px;
                border: 1px solid #ddd;
                border-radius: 4px;
                font-size: 14px;
                transition: border-color 0.2s;
                box-sizing: border-box;
            }
            
            .nw-form-group input:focus,
            .nw-form-group textarea:focus {
                outline: none;
                border-color: #0073aa;
                box-shadow: 0 0 0 2px rgba(0, 115, 170, 0.1);
            }
            
            .nw-selection-summary {
                background: #f9f9f9;
                padding: 15px;
                border-radius: 4px;
                margin-top: 10px;
                border: 1px solid #e5e5e5;
            }
            
            .nw-form-actions {
                display: flex;
                gap: 10px;
                justify-content: flex-end;
                margin-top: 30px;
            }
            
            .nw-form-success {
                padding: 30px;
                text-align: center;
            }
            
            .nw-form-success h4 {
                color: #28a745;
                margin-bottom: 15px;
                font-size: 1.3em;
            }
        ');
    }
}

// JavaScript Assets laden - nur wenn noch nicht existiert
// if ( ! function_exists( 'newwways_vc_plugin_assets' ) ) {
    
//     add_action('wp_enqueue_scripts', 'newwways_vc_plugin_assets');
    
//     function newwways_vc_plugin_assets() {
//         wp_enqueue_script(
//             'newwways-vc-stepper',
//             plugin_dir_url(__FILE__) . 'js/stepper.js',
//             [],
//             filemtime(plugin_dir_path(__FILE__) . 'js/stepper.js'),
//             true
//         );
//     }

// } // Ende Assets function_exists Check

// WPBakery Shortcode Klasse
if (class_exists('WPBakeryShortCode')) {
    class WPBakeryShortCode_Newwways_Wartungs_Und_Servicerechner extends WPBakeryShortCode {}
}