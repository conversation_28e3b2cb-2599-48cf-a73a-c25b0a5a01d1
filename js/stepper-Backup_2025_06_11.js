// document.addEventListener('DOMContentLoaded', function () {
//     // Alle Stepper-Container durchgehen
//     const steppers = document.querySelectorAll('.stepper');

//     steppers.forEach(function (stepper) {
//         const input = stepper.querySelector('.anzahl-input');
//         const upBtn = stepper.querySelector('.step-up');
//         const downBtn = stepper.querySelector('.step-down');

//         if (!input || !upBtn || !downBtn) return;

//         upBtn.addEventListener('click', function () {
//             input.stepUp();
//         });

//         downBtn.addEventListener('click', function () {
//             if (parseInt(input.value) > 1) {
//                 input.stepDown();
//             }
//         });
//     });
// });


console.log("newwways stepper.js wurde geladen!");
function berechneSummenProContainer() {
    console.log('Berechne Summen pro Container'); // Debugging

    document.querySelectorAll('.nw-rechner-container-wrapper').forEach(function (container) {
        console.log('Container:', container); 
        
        let summe = 0;

        // Alle Elemente im Container durchgehen
        const blocks = container.querySelectorAll('.vc-mein-rechner-element');
        console.log('Gefundene Blöcke:', blocks); // Debugging
        console.log("Blöcke im Container:", blocks.length);
        
        blocks.forEach(function (block) {
            const input = block.querySelector('.anzahl-input');
            const preisFeld = block.querySelector('.preis');
            
            if (!input || !preisFeld) {
                console.warn('Fehlende Felder in Block:', block); // Debugging
                return;
            }
            console.log("Datensatz:", preisFeld.dataset.preis);
            const preis = parseFloat(preisFeld.dataset.preis);
            const anzahl = parseInt(input.value) || 0;
            
            if (!isNaN(preis) && !isNaN(anzahl)) {
                summe += anzahl * preis;
            }
        });

        // Gesamtsumme anzeigen
        const mwst = summe * 0.19;
        const brutto = summe + mwst;

        const gesamtFeld = container.querySelector('.netto-summe-wert');
        const mwstFeld = container.querySelector('.mwst-summe-wert');
        const bruttoFeld = container.querySelector('.brutto-summe-wert');

        if (gesamtFeld) {
            gesamtFeld.textContent = summe.toFixed(2).replace('.', ',');
        }
        if (mwstFeld) {
            mwstFeld.textContent = mwst.toFixed(2).replace('.', ',');
        }
        if (bruttoFeld) {
            bruttoFeld.textContent = brutto.toFixed(2).replace('.', ',');
        }
    });
}

document.addEventListener('DOMContentLoaded', function () {
    console.log('DOMContentLoaded'); // Debugging
    
    document.querySelectorAll('.stepper').forEach(function (stepper) {
        console.log('Stepper:', stepper); // Debugging

        const input = stepper.querySelector('.anzahl-input');
        const upBtn = stepper.querySelector('.step-up');
        const downBtn = stepper.querySelector('.step-down');

        if (!input || !upBtn || !downBtn) {
            console.warn('Fehlende Felder in Stepper:', stepper); // Debugging
            return;
        }

        const update = () => berechneSummenProContainer();

        downBtn.addEventListener("click", () => {
            const minValue = parseInt(input.dataset.min) || 0
            if (parseInt(input.value) > minValue) {
                console.log("DOWN")
                input.stepDown()
                update()
            }
        })

        upBtn.addEventListener("click", () => {
            const maxValue = parseInt(input.dataset.max)
            if (!maxValue || parseInt(input.value) < maxValue) {
                console.log("UP")
                input.stepUp()
                update()
            }
        })

        input.addEventListener('change', update);
    });

    // Initial berechnen
    berechneSummenProContainer();
});
